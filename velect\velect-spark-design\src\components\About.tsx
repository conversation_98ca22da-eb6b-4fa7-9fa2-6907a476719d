
import { useScrollAnimation } from "@/hooks/useScrollAnimation";

const About = () => {
  const { ref, isVisible } = useScrollAnimation();

  return (
    <section id="about" className="py-20 relative bg-cyber-gray">
      <div className="container mx-auto px-4 lg:px-8 relative z-10">
        <div 
          ref={ref}
          className={`text-center mb-16 transition-all duration-1000 ${
            isVisible ? 'fade-in-up animate' : 'fade-in-up'
          }`}
        >
          <h2 className="text-5xl lg:text-6xl font-bold mb-6">
            <span className="gradient-cyber bg-clip-text text-transparent neon-text">ELECTRICIANS SERVICING MELBOURNE</span>
            <br />
            <span className="text-foreground">AND SURROUNDING SUBURBS</span>
          </h2>
        </div>

        <div className="max-w-4xl mx-auto text-center">
          <div className={`transition-all duration-1000 ${
            isVisible ? 'fade-in-up animate' : 'fade-in-up'
          }`}>
            <h3 className="text-3xl font-bold mb-8 text-cyber-blue">About us</h3>
            <p className="text-lg text-muted-foreground leading-relaxed">
              Bitz Electrical was founded by Jonathan Bitzikopoulos, an electrician in Melbourne with over 20 years of industry experience in the commercial, residential and construction sectors.
            </p>
            <br />
            <p className="text-lg text-muted-foreground leading-relaxed">
              Over this time, Jonathan has gained exceptional knowledge and expertise in a vast range of electrical services. He is hardworking, trustworthy and prides himself on the quality of his work. Customer satisfaction is his number one priority.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;
