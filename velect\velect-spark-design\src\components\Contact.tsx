
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { MapPin, Phone, Mail, Clock } from "lucide-react";
import { useScrollAnimation } from "@/hooks/useScrollAnimation";

const Contact = () => {
  const { ref, isVisible } = useScrollAnimation();

  return (
    <section id="contact" className="py-20 relative bg-background">
      <div className="container mx-auto px-4 lg:px-8">
        <div 
          ref={ref}
          className={`text-center mb-16 transition-all duration-1000 ${
            isVisible ? 'fade-in-up animate' : 'fade-in-up'
          }`}
        >
          <h2 className="text-5xl lg:text-6xl font-bold mb-6">
            Get In <span className="gradient-cyber bg-clip-text text-transparent neon-text">Touch</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Ready to power up your project? Contact us for a free quote today.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
          <div className={`transition-all duration-1000 ${
            isVisible ? 'slide-in-left animate' : 'slide-in-left'
          }`}>
            <div className="space-y-8">
              {[
                {
                  icon: <MapPin className="h-6 w-6" />,
                  title: "Office Address",
                  details: ["13 Berry St Clyde", "NSW 2142", "Australia"]
                },
                {
                  icon: <Phone className="h-6 w-6" />,
                  title: "Phone",
                  details: ["0406 599 271"]
                },
                {
                  icon: <Mail className="h-6 w-6" />,
                  title: "Email",
                  details: ["<EMAIL>"]
                },
                {
                  icon: <Clock className="h-6 w-6" />,
                  title: "Business Hours",
                  details: ["Monday–Friday: 7:00 AM – 5:00 PM", "Saturday: 7:00 AM – 1:00 PM"]
                }
              ].map((item, index) => (
                <div key={index} className="flex items-start space-x-4">
                  <div className="bg-gradient-cyber rounded-xl p-3 text-cyber-dark sharp-card">
                    {item.icon}
                  </div>
                  <div>
                    <h3 className="font-bold text-xl text-foreground mb-2">{item.title}</h3>
                    {item.details.map((detail, i) => (
                      <p key={i} className="text-muted-foreground">{detail}</p>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className={`transition-all duration-1000 ${
            isVisible ? 'slide-in-right animate' : 'slide-in-right'
          }`}>
            <Card className="glass-effect border-border sharp-card">
              <CardHeader>
                <CardTitle className="text-2xl text-foreground">Send us a message</CardTitle>
                <CardDescription>
                  Fill out the form below and we'll get back to you within 24 hours.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="name">Name</Label>
                  <Input id="name" placeholder="Your name" type="text" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input id="email" placeholder="Your email" type="email" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="message">Message</Label>
                  <Input id="message" placeholder="Write your message here" type="text" />
                </div>
                <Button className="w-full">Send Message</Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Contact;
