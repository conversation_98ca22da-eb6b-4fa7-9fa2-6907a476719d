
import { Button } from "@/components/ui/button";
import { Menu } from "lucide-react";
import { useScrollAnimation } from "@/hooks/useScrollAnimation";

const Header = () => {
  const { ref, isVisible } = useScrollAnimation();

  return (
    <header 
      ref={ref}
      className={`glass-effect border-b border-border sticky top-0 z-50 transition-all duration-1000 ${
        isVisible ? 'fade-in-up animate' : 'fade-in-up'
      }`}
    >
      <div className="container mx-auto px-4 lg:px-8">
        <div className="flex items-center justify-between h-16 lg:h-20">
          {/* Logo */}
          <div className="flex items-center space-x-3">
            <img 
              src="/lovable-uploads/539fbedb-8b65-4b44-bf89-4ab0a409c415.png" 
              alt="Bitz Electrical Logo" 
              className="h-12 w-auto"
            />
          </div>

          {/* Navigation - Hidden on mobile */}
          <nav className="hidden lg:flex items-center space-x-8">
            <a href="#home" className="text-foreground hover:text-cyber-blue transition-all duration-300 hover:neon-text">Home</a>
            <a href="#services" className="text-foreground hover:text-cyber-blue transition-all duration-300 hover:neon-text">Services</a>
            <a href="#about" className="text-foreground hover:text-cyber-blue transition-all duration-300 hover:neon-text">About</a>
            <a href="#reviews" className="text-foreground hover:text-cyber-blue transition-all duration-300 hover:neon-text">Reviews</a>
            <a href="#contact" className="text-foreground hover:text-cyber-blue transition-all duration-300 hover:neon-text">Contact</a>
          </nav>

          {/* CTA Button and Mobile Menu */}
          <div className="flex items-center space-x-4">
            <Button className="hidden lg:block btn-cyber">
              Get Quote
            </Button>
            <Button variant="ghost" size="icon" className="lg:hidden text-cyber-blue hover:bg-cyber-blue/20">
              <Menu className="h-6 w-6" />
            </Button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
