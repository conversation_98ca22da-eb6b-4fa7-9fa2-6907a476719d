
import { But<PERSON> } from "@/components/ui/button";
import { Zap } from "lucide-react";
import { useScrollAnimation } from "@/hooks/useScrollAnimation";

const Hero = () => {
  const { ref, isVisible } = useScrollAnimation();

  return (
    <section 
      id="home" 
      ref={ref}
      className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-cyber-dark via-background to-cyber-dark"
    >
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-cyber-blue/10 rounded-full blur-3xl float-animation"></div>
        <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-cyber-blue-dark/10 rounded-full blur-3xl float-animation" style={{animationDelay: '2s'}}></div>
        <div className="absolute top-1/2 left-1/2 w-96 h-96 bg-cyber-blue/5 rounded-full blur-3xl float-animation" style={{animationDelay: '4s'}}></div>
      </div>

      {/* Content */}
      <div className="relative z-10 container mx-auto px-4 lg:px-8 text-center">
        <div className={`max-w-5xl mx-auto transition-all duration-1000 ${
          isVisible ? 'fade-in-up animate' : 'fade-in-up'
        }`}>
          <div className="mb-8">
            <div className="inline-flex items-center space-x-2 bg-cyber-blue/20 px-6 py-2 sharp-card border border-cyber-blue/30 mb-6">
              <Zap className="h-5 w-5 text-cyber-blue" />
              <span className="text-cyber-blue font-semibold">Electrical and Data Specialists</span>
            </div>
          </div>

          <h1 className="text-5xl lg:text-7xl font-bold mb-8 leading-tight">
            <span className="text-foreground">Residential and Commercial</span>
            <br />
            <span className="gradient-cyber bg-clip-text text-transparent neon-text">Electrician Melbourne</span>
          </h1>
          
          <p className="text-xl lg:text-2xl text-muted-foreground mb-12 max-w-3xl mx-auto leading-relaxed">
            No job is too big or too small!
          </p>
          
          <div className={`flex flex-col sm:flex-row gap-6 justify-center transition-all duration-1000 delay-300 ${
            isVisible ? 'scale-in animate' : 'scale-in'
          }`}>
            <Button className="btn-cyber">
              Get A Free Quote
            </Button>
            <Button className="btn-outline-cyber">
              Call 0406 599 271
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
