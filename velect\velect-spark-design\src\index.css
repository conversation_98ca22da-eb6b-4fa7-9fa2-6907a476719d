@tailwind base;
@tailwind components;
@tailwind utilities;

/* Futuristic design system with black and blue theme */

@layer base {
  :root {
    --background: 0 0% 8%;
    --foreground: 210 40% 98%;

    --card: 0 0% 12%;
    --card-foreground: 210 40% 98%;

    --popover: 0 0% 12%;
    --popover-foreground: 210 40% 98%;

    --primary: 200 100% 60%;
    --primary-foreground: 0 0% 8%;

    --secondary: 200 80% 50%;
    --secondary-foreground: 0 0% 8%;

    --muted: 0 0% 15%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 200 100% 60%;
    --accent-foreground: 0 0% 8%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 0 0% 20%;
    --input: 0 0% 20%;
    --ring: 200 100% 60%;

    --radius: 1rem;

    --cyber-blue: 200 100% 60%;
    --cyber-blue-dark: 200 80% 50%;
    --cyber-dark: 0 0% 8%;
    --cyber-light: 210 40% 98%;
    --cyber-gray: 0 0% 15%;
    --neon-glow: 200 100% 60%;
  }

  .dark {
    --background: 0 0% 8%;
    --foreground: 210 40% 98%;

    --card: 0 0% 12%;
    --card-foreground: 210 40% 98%;

    --popover: 0 0% 12%;
    --popover-foreground: 210 40% 98%;

    --primary: 200 100% 60%;
    --primary-foreground: 0 0% 8%;

    --secondary: 200 80% 50%;
    --secondary-foreground: 0 0% 8%;

    --muted: 0 0% 15%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 200 100% 60%;
    --accent-foreground: 0 0% 8%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 0 0% 20%;
    --input: 0 0% 20%;
    --ring: 200 100% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, system-ui, sans-serif;
  }

  html {
    scroll-behavior: smooth;
  }
}

/* Futuristic utilities */
@layer utilities {
  .text-cyber-blue {
    color: hsl(var(--cyber-blue));
  }
  
  .text-cyber-blue-dark {
    color: hsl(var(--cyber-blue-dark));
  }
  
  .bg-cyber-blue {
    background-color: hsl(var(--cyber-blue));
  }
  
  .bg-cyber-blue-dark {
    background-color: hsl(var(--cyber-blue-dark));
  }
  
  .bg-cyber-gray {
    background-color: hsl(var(--cyber-gray));
  }
  
  .bg-gradient-cyber {
    background: linear-gradient(135deg, hsl(var(--cyber-blue)), hsl(var(--cyber-blue-dark)));
  }

  .gradient-cyber {
    background: linear-gradient(135deg, hsl(var(--cyber-blue)), hsl(var(--cyber-blue-dark)));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .neon-glow {
    box-shadow: 0 0 20px hsl(var(--neon-glow)), 0 0 40px hsl(var(--neon-glow)), 0 0 60px hsl(var(--neon-glow));
  }

  .neon-text {
    color: hsl(var(--cyber-blue));
  }

  .glass-effect {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .cyber-border {
    border: 2px solid;
    border-image: linear-gradient(45deg, hsl(var(--cyber-blue)), hsl(var(--cyber-blue-dark))) 1;
  }

  /* Sharp edges for cards */
  .sharp-card {
    border-radius: 0;
  }
}

/* Scroll animations */
@layer utilities {
  .fade-in-up {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.8s ease-out;
  }

  .fade-in-up.animate {
    opacity: 1;
    transform: translateY(0);
  }

  .scale-in {
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.6s ease-out;
  }

  .scale-in.animate {
    opacity: 1;
    transform: scale(1);
  }

  .slide-in-left {
    opacity: 0;
    transform: translateX(-50px);
    transition: all 0.8s ease-out;
  }

  .slide-in-left.animate {
    opacity: 1;
    transform: translateX(0);
  }

  .slide-in-right {
    opacity: 0;
    transform: translateX(50px);
    transition: all 0.8s ease-out;
  }

  .slide-in-right.animate {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Futuristic button styles */
@layer components {
  .btn-cyber {
    @apply px-8 py-4 rounded-2xl font-bold text-lg transition-all duration-300 transform hover:scale-105;
    background: linear-gradient(135deg, hsl(var(--cyber-blue)), hsl(var(--cyber-blue-dark)));
    box-shadow: 0 4px 15px rgba(0, 255, 255, 0.3);
  }

  .btn-cyber:hover {
    box-shadow: 0 6px 25px rgba(0, 255, 255, 0.5);
  }

  .btn-outline-cyber {
    @apply px-8 py-4 rounded-2xl font-bold text-lg border-2 transition-all duration-300 transform hover:scale-105;
    border-color: hsl(var(--cyber-blue));
    color: hsl(var(--cyber-blue));
    background: transparent;
  }

  .btn-outline-cyber:hover {
    background: hsl(var(--cyber-blue));
    color: hsl(var(--cyber-dark));
    box-shadow: 0 0 20px hsl(var(--cyber-blue));
  }
}

/* Keyframes for animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 20px hsl(var(--cyber-blue)); }
  50% { box-shadow: 0 0 40px hsl(var(--cyber-blue)), 0 0 60px hsl(var(--cyber-blue)); }
}

.float-animation {
  animation: float 6s ease-in-out infinite;
}

.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}
