
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Zap, Cpu, Home, Building, Lightbulb, Settings, Camera, Battery } from "lucide-react";
import { useScrollAnimation } from "@/hooks/useScrollAnimation";

const Services = () => {
  const { ref, isVisible } = useScrollAnimation();

  const services = [
    {
      icon: <Zap className="h-8 w-8" />,
      title: "General Electrical Services",
      description: "Complete electrical solutions including lighting installations, power points, safety switches, and routine maintenance for residential and commercial properties."
    },
    {
      icon: <Cpu className="h-8 w-8" />,
      title: "Electrical Design & Planning",
      description: "Custom electrical design and layout planning for residential, commercial, and industrial projects ensuring optimal performance and safety."
    },
    {
      icon: <Home className="h-8 w-8" />,
      title: "Residential Electrical Services",
      description: "Comprehensive home electrical solutions including wiring, switchboard upgrades, lighting design, and safety inspections."
    },
    {
      icon: <Building className="h-8 w-8" />,
      title: "Commercial Electrical Services",
      description: "Tailored services for offices and shops covering fit-outs, maintenance, data cabling, and energy-efficient lighting systems."
    },
    {
      icon: <Lightbulb className="h-8 w-8" />,
      title: "Lighting Installation & Design",
      description: "Custom lighting setups for indoors and outdoors including LED, feature lights, security lighting, and sensor lights."
    },
    {
      icon: <Settings className="h-8 w-8" />,
      title: "Switchboard Upgrades & Repairs",
      description: "Upgrade old or unsafe switchboards to modern safety standards with circuit breakers, RCDs, and load management."
    },
    {
      icon: <Camera className="h-8 w-8" />,
      title: "CCTV & Security Systems",
      description: "Professional installation of high-resolution CCTV systems with remote viewing, motion detection, and data storage capabilities."
    },
    {
      icon: <Battery className="h-8 w-8" />,
      title: "EV Charger Installation",
      description: "Supply and install electric vehicle charging stations for residential or commercial use, compatible with all EV models."
    }
  ];

  return (
    <section id="services" className="py-20 relative bg-background">
      <div className="container mx-auto px-4 lg:px-8 relative z-10">
        <div 
          ref={ref}
          className={`text-center mb-16 transition-all duration-1000 ${
            isVisible ? 'fade-in-up animate' : 'fade-in-up'
          }`}
        >
          <h2 className="text-5xl lg:text-6xl font-bold mb-6">
            Our <span className="gradient-cyber bg-clip-text text-transparent neon-text">Services</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Cutting-edge electrical solutions designed for the future, delivered today.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {services.map((service, index) => (
            <div
              key={index}
              className={`transition-all duration-1000 ${
                isVisible ? 'scale-in animate' : 'scale-in'
              }`}
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <Card className="h-full glass-effect border-border hover:border-cyber-blue/50 transition-all duration-300 transform hover:scale-105 sharp-card group">
                <CardHeader className="text-center pb-4">
                  <div className="mb-4 mx-auto w-16 h-16 bg-gradient-cyber sharp-card flex items-center justify-center text-cyber-dark transition-all duration-300 group-hover:neon-glow">
                    {service.icon}
                  </div>
                  <CardTitle className="text-lg leading-tight text-foreground">{service.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-center text-muted-foreground">
                    {service.description}
                  </CardDescription>
                </CardContent>
              </Card>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Services;
