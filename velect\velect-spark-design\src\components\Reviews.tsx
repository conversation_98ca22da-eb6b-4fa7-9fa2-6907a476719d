
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Star } from "lucide-react";
import { useScrollAnimation } from "@/hooks/useScrollAnimation";

const Reviews = () => {
  const { ref, isVisible } = useScrollAnimation();

  const reviews = [
    {
      name: "Rachel M.",
      location: "Castle Hill",
      rating: 5,
      text: "Very impressed with the level of detail in their electrical design. Everything was planned before the work began, and it all ran smoothly. Great workmanship!"
    },
    {
      name: "<PERSON>",
      location: "Parramatta", 
      rating: 5,
      text: "Reliable, fast, and clean – exactly what you want in an electrician. They installed downlights and extra power points throughout our home with zero hassle. Highly recommend!"
    },
    {
      name: "James K.",
      location: "Bankstown",
      rating: 5,
      text: "We had a complete switchboard upgrade done and the guys walked us through the whole process. Professional from start to finish – we finally feel safe in our home."
    },
    {
      name: "<PERSON><PERSON>",
      location: "Newtown",
      rating: 5,
      text: "I run a café and needed urgent fault finding and repairs. They arrived same-day, fixed the issue, and helped prevent it happening again. Can't thank them enough!"
    },
    {
      name: "<PERSON>",
      location: "Ryde",
      rating: 5,
      text: "Installed our CCTV system and intercom with expert precision. The team explained everything clearly and even showed us how to use the app. Brilliant service!"
    },
    {
      name: "Dominic G.",
      location: "Wetherill Park",
      rating: 5,
      text: "They took care of all the electrical and fire safety compliance for our warehouse – FIP, emergency lights, extinguishers, the lot. Also handled our AFSS sign-off. Top-tier service."
    }
  ];

  return (
    <section id="reviews" className="py-20 relative bg-background">
      <div className="container mx-auto px-4 lg:px-8">
        <div 
          ref={ref}
          className={`text-center mb-16 transition-all duration-1000 ${
            isVisible ? 'fade-in-up animate' : 'fade-in-up'
          }`}
        >
          <h2 className="text-5xl lg:text-6xl font-bold mb-6">
            Client <span className="gradient-cyber bg-clip-text text-transparent neon-text">Reviews</span>
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Don't just take our word for it - hear from our satisfied customers across Sydney.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {reviews.map((review, index) => (
            <div
              key={index}
              className={`transition-all duration-1000 ${
                isVisible ? 'scale-in animate' : 'scale-in'
              }`}
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <Card className="h-full glass-effect border-border hover:border-cyber-blue/50 transition-all duration-300 transform hover:scale-105 sharp-card">
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <CardTitle className="text-lg text-foreground">{review.name}</CardTitle>
                      <p className="text-sm text-cyber-blue">{review.location}</p>
                    </div>
                    <div className="flex text-yellow-400">
                      {[...Array(review.rating)].map((_, i) => (
                        <Star key={i} className="h-5 w-5 fill-current" />
                      ))}
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-muted-foreground leading-relaxed">
                    "{review.text}"
                  </CardDescription>
                </CardContent>
              </Card>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Reviews;
