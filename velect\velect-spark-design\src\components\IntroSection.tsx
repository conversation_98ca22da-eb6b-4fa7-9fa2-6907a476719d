
import { useScrollAnimation } from "@/hooks/useScrollAnimation";

const IntroSection = () => {
  const { ref, isVisible } = useScrollAnimation();

  return (
    <section className="relative bg-cyber-gray">
      {/* Mobile Layout */}
      <div className="lg:hidden">
        <div className="container mx-auto px-4">
          {/* Image on top for mobile */}
          <div className={`relative h-64 transition-all duration-1000 ${
            isVisible ? 'slide-in-right animate' : 'slide-in-right'
          }`}>
            <div className="absolute inset-0 overflow-hidden">
              <img 
                src="https://images.unsplash.com/photo-1518770660439-4636190af475" 
                alt="Professional Electrical Work" 
                className="absolute w-full h-full object-cover"
                style={{
                  clipPath: 'polygon(0% 0%, 100% 0%, 80% 100%, 0% 100%)',
                }}
              />
              {/* Blue light edge on bottom diagonal for mobile */}
              <div 
                className="absolute inset-0 pointer-events-none"
                style={{
                  background: 'linear-gradient(45deg, transparent 78%, hsl(var(--cyber-blue)) 80%, transparent 82%)',
                  boxShadow: '0 0 20px hsl(var(--cyber-blue)), 0 0 40px hsl(var(--cyber-blue))',
                  filter: 'blur(1px)'
                }}
              ></div>
            </div>
            
            {/* Floating elements */}
            <div className="absolute -top-6 -right-6 w-24 h-24 bg-cyber-blue/20 rounded-full blur-xl float-animation"></div>
            <div className="absolute -bottom-6 -left-6 w-32 h-32 bg-cyber-blue-dark/20 rounded-full blur-animation" style={{animationDelay: '2s'}}></div>
          </div>
          
          {/* Text below for mobile */}
          <div 
            ref={ref}
            className={`py-12 transition-all duration-1000 flex flex-col justify-center ${
              isVisible ? 'slide-in-left animate' : 'slide-in-left'
            }`}
          >
            <h2 className="text-3xl font-bold mb-6">
              Melbourne's <span className="gradient-cyber bg-clip-text text-transparent neon-text">Trusted, Fast, And Efficient</span> Electricians
            </h2>
            <p className="text-base text-muted-foreground mb-4 leading-relaxed">
              Bitz Electrical is your one-stop solution for all your electrical work. We're a certified electrician in Melbourne, VIC, who prioritises safety and quality in everything we do.
            </p>
            <p className="text-base text-muted-foreground mb-4 leading-relaxed">
              We prioritise efficient energy use, helping you save on electricity bills. Our electricians integrate electrical functionality with aesthetics, ensuring that all installations and repairs enhance the visual appeal of your home or office.
            </p>
            <p className="text-base text-muted-foreground leading-relaxed">
              We offer a free quote to provide you with transparent and fair pricing. Don't hesitate to call us today and experience the difference that our electrician can make!
            </p>
          </div>
        </div>
      </div>

      {/* Desktop Layout */}
      <div className="hidden lg:flex items-stretch min-h-[600px]">
        {/* Text section - constrained to container */}
        <div className="w-1/2">
          <div className="container mx-auto px-4 lg:px-8 h-full">
            <div 
              ref={ref}
              className={`transition-all duration-1000 flex flex-col justify-center h-full ${
                isVisible ? 'slide-in-left animate' : 'slide-in-left'
              }`}
            >
              <h2 className="text-4xl lg:text-5xl font-bold mb-8">
                Melbourne's <span className="gradient-cyber bg-clip-text text-transparent neon-text">Trusted, Fast, And Efficient</span> Electricians
              </h2>
              <p className="text-lg text-muted-foreground mb-6 leading-relaxed">
                Bitz Electrical is your one-stop solution for all your electrical work. We're a certified electrician in Melbourne, VIC, who prioritises safety and quality in everything we do.
              </p>
              <p className="text-lg text-muted-foreground mb-6 leading-relaxed">
                We prioritise efficient energy use, helping you save on electricity bills. Our electricians integrate electrical functionality with aesthetics, ensuring that all installations and repairs enhance the visual appeal of your home or office.
              </p>
              <p className="text-lg text-muted-foreground leading-relaxed">
                We offer a free quote to provide you with transparent and fair pricing. Don't hesitate to call us today and experience the difference that our electrician can make!
              </p>
            </div>
          </div>
        </div>
        
        {/* Image section - extends to screen edge */}
        <div className="w-1/2 relative">
          <div className={`absolute inset-0 transition-all duration-1000 ${
            isVisible ? 'slide-in-right animate' : 'slide-in-right'
          }`}>
            <div className="absolute inset-0 overflow-hidden">
              <img 
                src="https://images.unsplash.com/photo-1518770660439-4636190af475" 
                alt="Professional Electrical Work" 
                className="absolute w-full h-full object-cover"
                style={{
                  clipPath: 'polygon(20% 0%, 100% 0%, 100% 100%, 0% 100%)',
                  left: 0,
                  top: 0
                }}
              />
              {/* Blue light edge on diagonal - always visible glow */}
              <div 
                className="absolute inset-0 pointer-events-none"
                style={{
                  background: 'linear-gradient(135deg, hsl(var(--cyber-blue)) 0%, transparent 2%)',
                  clipPath: 'polygon(20% 0%, 22% 0%, 2% 100%, 0% 100%)',
                  boxShadow: '0 0 20px hsl(var(--cyber-blue)), 0 0 40px hsl(var(--cyber-blue))',
                  filter: 'blur(1px)'
                }}
              ></div>
            </div>
            
            {/* Floating elements */}
            <div className="absolute -top-6 -right-6 w-24 h-24 bg-cyber-blue/20 rounded-full blur-xl float-animation"></div>
            <div className="absolute -bottom-6 -left-6 w-32 h-32 bg-cyber-blue-dark/20 rounded-full blur-animation" style={{animationDelay: '2s'}}></div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default IntroSection;
