
import { Zap } from "lucide-react";

const Footer = () => {
  return (
    <footer className="bg-gradient-to-t from-cyber-dark to-background border-t border-border py-16">
      <div className="container mx-auto px-4 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-12">
          {/* Company Info */}
          <div className="space-y-6">
            <div className="flex items-center space-x-3">
              <img 
                src="/lovable-uploads/539fbedb-8b65-4b44-bf89-4ab0a409c415.png" 
                alt="Velect Logo" 
                className="h-12 w-auto"
              />
            </div>
            <p className="text-sm text-muted-foreground leading-relaxed">
              Pioneering the future of electrical services across Melbourne. 
              Licensed, insured, and committed to technological excellence.
            </p>
          </div>

          {/* Services */}
          <div>
            <h4 className="font-bold mb-6 text-cyber-blue">Services</h4>
            <ul className="space-y-3 text-sm text-muted-foreground">
              <li><a href="#" className="hover:text-cyber-blue transition-colors">Residential Electrical</a></li>
              <li><a href="#" className="hover:text-cyber-blue transition-colors">Commercial Solutions</a></li>
              <li><a href="#" className="hover:text-cyber-blue transition-colors">CCTV Installation</a></li>
              <li><a href="#" className="hover:text-cyber-blue transition-colors">EV Charger Setup</a></li>
            </ul>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="font-bold mb-6 text-cyber-blue">Quick Links</h4>
            <ul className="space-y-3 text-sm text-muted-foreground">
              <li><a href="#about" className="hover:text-cyber-blue transition-colors">About Us</a></li>
              <li><a href="#services" className="hover:text-cyber-blue transition-colors">Services</a></li>
              <li><a href="#reviews" className="hover:text-cyber-blue transition-colors">Reviews</a></li>
              <li><a href="#contact" className="hover:text-cyber-blue transition-colors">Contact</a></li>
            </ul>
          </div>

          {/* Contact */}
          <div>
            <h4 className="font-bold mb-6 text-cyber-blue">Contact Info</h4>
            <div className="space-y-3 text-sm text-muted-foreground">
              <p>13 Berry St Clyde, NSW 2142</p>
              <p className="text-cyber-blue">Phone: 0406 599 271</p>
              <p>Email: <EMAIL></p>
            </div>
          </div>
        </div>

        <div className="border-t border-border mt-12 pt-8 text-center text-sm text-muted-foreground">
          <p>&copy; 2024 Velect. All rights reserved. | Licensed Electrical Contractor</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
